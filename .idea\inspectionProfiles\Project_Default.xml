<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="HttpUrlsUsage" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="5">
            <item index="0" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="1" class="java.lang.String" itemvalue="email_validator" />
            <item index="2" class="java.lang.String" itemvalue="wtforms" />
            <item index="3" class="java.lang.String" itemvalue="click" />
            <item index="4" class="java.lang.String" itemvalue="Flask-Login" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>