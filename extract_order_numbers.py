#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取CSV文件中的系统订单号、订单金额和创建时间
"""

import csv
import re
import sys
import datetime
import requests
import time
import asyncio
import aiohttp
from pathlib import Path

def extract_order_data(csv_file_path, output_file=None):
    """
    从CSV文件中提取系统订单号、订单金额和创建时间
    
    Args:
        csv_file_path (str): CSV文件路径
        output_file (str, optional): 输出文件路径，如果不指定则打印到控制台
    
    Returns:
        list: 提取到的订单数据列表，每个元素为字典
    """
    order_data = []
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            # 使用csv.reader来处理CSV文件
            csv_reader = csv.reader(file)
            
            # 跳过标题行
            header = next(csv_reader)
            print(f"CSV文件标题: {header}")
            
            # 处理每一行数据
            for row_num, row in enumerate(csv_reader, start=2):
                if row and len(row) >= 15:  # 确保行不为空且有足够的列
                    # 第一列是系统订单号
                    order_number = row[0].strip()
                    
                    # 清理订单号格式（去除引号和等号）
                    # 原始格式: ="2025063021384579863"
                    cleaned_order = re.sub(r'^="([^"]+)"$', r'\1', order_number)
                    
                    # 第六列是订单金额
                    order_amount = row[6].strip()
                    
                    # 第十四列是创建时间
                    create_time = row[14].strip()
                    
                    # 转换创建时间为时间戳
                    timestamp = None
                    try:
                        # 解析时间格式: 2025-06-30 21:38:45
                        dt = datetime.datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                        timestamp = int(dt.timestamp())
                    except (ValueError, IndexError):
                        # 如果时间格式不正确，使用当前时间戳
                        timestamp = int(datetime.datetime.now().timestamp())
                    
                    if cleaned_order:
                        order_data.append({
                            'order_number': cleaned_order,
                            'amount': order_amount,
                            'create_time': create_time,
                            'timestamp': timestamp
                        })
    
    except FileNotFoundError:
        print(f"错误: 找不到文件 '{csv_file_path}'")
        return []
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return []
    
    # 输出结果
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("系统订单号,订单金额,创建时间,时间戳\n")
                for order in order_data:
                    f.write(f"{order['order_number']},{order['amount']},{order['create_time']},{order['timestamp']}\n")
            print(f"已成功提取 {len(order_data)} 条订单数据到文件: {output_file}")
        except Exception as e:
            print(f"写入输出文件时发生错误: {e}")
    else:
        print(f"共提取到 {len(order_data)} 条订单数据:")
        for order in order_data:
            print(f"{order['order_number']},{order['amount']},{order['create_time']},{order['timestamp']}")
    
    return order_data

async def submit_single_order(session, order, api_url, headers, order_index, total_count):
    """异步提交单个订单"""
    try:
        # 准备请求数据
        data = {
            'out_trade_no': order['order_number'],
            'price': order['amount'],
            'create_time': str(order['timestamp'])
        }
        
        print(f"正在提交第 {order_index}/{total_count} 条数据: {order['order_number']}")
        
        # 发送异步POST请求
        async with session.post(api_url, headers=headers, data=data, timeout=aiohttp.ClientTimeout(total=30)) as response:
            response_text = await response.text()
            
            # 检查响应状态
            if response.status == 200:
                print(f"  ✓ 成功 - 状态码: {response.status}")
                return {
                    'order_number': order['order_number'],
                    'success': True,
                    'status_code': response.status,
                    'response': response_text[:200]
                }
            else:
                print(f"  ✗ 失败 - 状态码: {response.status}")
                print(f"  ✗ 失败 - 响应: {response_text}")
                return {
                    'order_number': order['order_number'],
                    'success': False,
                    'status_code': response.status,
                    'response': response_text[:200]
                }
                
    except asyncio.TimeoutError:
        print(f"  ✗ 请求超时: {order['order_number']}")
        return {
            'order_number': order['order_number'],
            'success': False,
            'error': '请求超时'
        }
    except Exception as e:
        print(f"  ✗ 请求异常: {order['order_number']} - {e}")
        return {
            'order_number': order['order_number'],
            'success': False,
            'error': str(e)
        }

async def submit_to_api_async(order_data, api_url=None, max_concurrent=10):
    """
    异步将订单数据提交到API接口
    
    Args:
        order_data (list): 订单数据列表
        api_url (str): API接口地址
        max_concurrent (int): 最大并发数
    
    Returns:
        dict: 提交结果统计
    """
    if not api_url:
        api_url = 'http://**********/replacebuy/shop/ordercreatenew/create.html'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Accept-Language': 'zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7',
        'Cache-Control': 'no-cache',
        'Origin': 'http://**********',
        'Pragma': 'no-cache',
        'Referer': 'http://**********/shop.html',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'X-Requested-With': 'XMLHttpRequest',
        'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    print(f"\n开始异步提交数据到接口: {api_url}")
    print(f"最大并发数: {max_concurrent}")
    print("-" * 60)
    
    # 创建信号量限制并发数
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def submit_with_semaphore(session, order, order_index):
        async with semaphore:
            return await submit_single_order(session, order, api_url, headers, order_index, len(order_data))
    
    # 创建aiohttp会话
    connector = aiohttp.TCPConnector(limit=max_concurrent, limit_per_host=max_concurrent)
    async with aiohttp.ClientSession(connector=connector) as session:
        # 创建所有任务
        tasks = []
        for i, order in enumerate(order_data, 1):
            task = submit_with_semaphore(session, order, i)
            tasks.append(task)
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 统计结果
    success_count = 0
    failed_count = 0
    failed_orders = []
    
    for result in results:
        if isinstance(result, Exception):
            failed_count += 1
            failed_orders.append({
                'order_number': 'unknown',
                'error': str(result)
            })
        elif result['success']:
            success_count += 1
        else:
            failed_count += 1
            failed_orders.append(result)
    
    # 输出统计结果
    print("\n" + "=" * 60)
    print(f"异步提交完成！")
    print(f"成功: {success_count} 条")
    print(f"失败: {failed_count} 条")
    print(f"总计: {len(order_data)} 条")
    
    if failed_orders:
        print(f"\n失败的订单详情:")
        for failed in failed_orders[:10]:  # 只显示前10个失败记录
            print(f"  订单号: {failed['order_number']}")
            if 'status_code' in failed:
                print(f"    状态码: {failed['status_code']}")
                print(f"    响应: {failed['response']}")
            if 'error' in failed:
                print(f"    错误: {failed['error']}")
            print()
    
    return {
        'success_count': success_count,
        'failed_count': failed_count,
        'total_count': len(order_data),
        'failed_orders': failed_orders
    }

def submit_to_api(order_data, api_url=None):
    """
    同步提交函数（保留兼容性）
    """
    return asyncio.run(submit_to_api_async(order_data, api_url))

def main():
    """主函数"""
    # 默认CSV文件路径
    csv_file = "order_2025-06-01 00_00_00_2025-07-07 00_00_00.csv"
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        csv_file = sys.argv[1]
    
    # 检查文件是否存在
    if not Path(csv_file).exists():
        print(f"错误: 文件 '{csv_file}' 不存在")
        print("请确保CSV文件在当前目录下，或者提供正确的文件路径")
        return
    
    # 输出文件路径（可选）
    output_file = None
    
    # API接口地址（可选）
    api_url = None
    if len(sys.argv) > 3:
        api_url = sys.argv[3]
    
    # 是否提交到API（可选）
    submit_api = False
    max_concurrent = 10  # 默认并发数
    
    if len(sys.argv) > 2 and sys.argv[2].lower() in ['true', 'yes', '1', 'submit']:
        submit_api = True
        output_file = None  # 如果只是提交到API，不保存文件
    elif len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    # 并发数参数（可选）
    if len(sys.argv) > 3 and sys.argv[3].isdigit():
        max_concurrent = int(sys.argv[3])
    
    print(f"正在处理文件: {csv_file}")
    print("-" * 50)
    
    # 提取订单数据
    order_data = extract_order_data(csv_file, output_file)
    
    if order_data:
        print(f"\n提取完成！共找到 {len(order_data)} 条订单数据")
        
        # 如果指定了提交到API，则执行提交
        if submit_api:
            asyncio.run(submit_to_api_async(order_data, api_url, max_concurrent))
        
    else:
        print("未找到任何订单数据")

if __name__ == "__main__":
    main() 