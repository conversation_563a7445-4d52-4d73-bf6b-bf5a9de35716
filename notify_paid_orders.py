#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取系统订单号、完成时间（时间戳）、支付状态，并将支付状态为已支付的订单异步提交到指定接口
"""

import csv
import sys
import datetime
import asyncio
import aiohttp

API_URL = 'http://**********/pay/pay2/notify.html'
COOKIE = 'think_lang=zh-cn; PHPSESSID=66c1dbc685264f09f65be3eb01490b78'

# 提取数据
def extract_paid_orders(csv_file):
    paid_orders = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)
        for row in reader:
            if len(row) < 17:
                continue
            order_no = row[0].strip().replace('="','').replace('"','')
            finish_time = row[15].strip()
            pay_status = row[16].strip()
            if pay_status == '已支付':
                # 转换完成时间为时间戳
                try:
                    dt = datetime.datetime.strptime(finish_time, '%Y-%m-%d %H:%M:%S')
                    finish_ts = int(dt.timestamp())
                except Exception:
                    finish_ts = ''
                paid_orders.append({
                    'order_no': order_no,
                    'finish_ts': finish_ts,
                    'pay_status': pay_status
                })
    return paid_orders

# 异步提交
async def submit_order(session, order, index, total):
    headers = {
        'Cookie': COOKIE
    }
    data = {
        'out_trade_no': order['order_no'],
        'pay_type': 'wechatpay',
        'pay_time': str(order['finish_ts']),
    }
    try:
        print(f"[{index}/{total}] 提交订单: {order['order_no']} pay_time: {order['finish_ts']}")
        async with session.post(API_URL, data=data, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as resp:
            text = await resp.text()
            if resp.status == 200:
                print(f"  ✓ 成功 - 状态码: {resp.status}")
            else:
                print(f"  ✗ 失败 - 状态码: {resp.status} 响应: {text}")
    except Exception as e:
        print(f"  ✗ 异常: {e}")

async def main(csv_file, max_concurrent=10):
    orders = extract_paid_orders(csv_file)
    print(f"共找到已支付订单 {len(orders)} 条，开始异步提交...")
    connector = aiohttp.TCPConnector(limit=max_concurrent)
    semaphore = asyncio.Semaphore(max_concurrent)
    async with aiohttp.ClientSession(connector=connector) as session:
        tasks = []
        for idx, order in enumerate(orders, 1):
            async def sem_task(order=order, idx=idx):
                async with semaphore:
                    await submit_order(session, order, idx, len(orders))
            tasks.append(sem_task())
        await asyncio.gather(*tasks)
    print("全部提交完成！")


# async def main(csv_file, max_concurrent=10):
#     orders = extract_paid_orders(csv_file)
#     print(f"共找到已支付订单 {len(orders)} 条，开始异步提交...")

#     # 只取第一条用于测试
#     if orders:
#         orders = [orders[0]]
#     else:
#         print("没有已支付订单！")
#         return

#     connector = aiohttp.TCPConnector(limit=max_concurrent)
#     semaphore = asyncio.Semaphore(max_concurrent)
#     async with aiohttp.ClientSession(connector=connector) as session:
#         tasks = []
#         for idx, order in enumerate(orders, 1):
#             async def sem_task(order=order, idx=idx):
#                 async with semaphore:
#                     await submit_order(session, order, idx, len(orders))
#             tasks.append(sem_task())
#         await asyncio.gather(*tasks)
#     print("全部提交完成！")

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print('用法: python notify_paid_orders.py <csv文件> [并发数]')
        sys.exit(1)
    csv_file = sys.argv[1]
    max_concurrent = int(sys.argv[2]) if len(sys.argv) > 2 and sys.argv[2].isdigit() else 10
    asyncio.run(main(csv_file, max_concurrent)) 