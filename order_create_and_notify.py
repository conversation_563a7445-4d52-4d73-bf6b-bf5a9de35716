#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步批量创建订单并回调支付（逐笔串行，成功才回调）
"""

import csv
import sys
import datetime
import requests
import json

CREATE_URL = 'http://**********/replacebuy/shop/ordercreatenew/create.html'
NOTIFY_URL = 'http://**********/pay/pay2/notify.html'
COOKIE = 'think_lang=zh-cn; PHPSESSID=66c1dbc685264f09f65be3eb01490b78'

# 读取订单数据
def extract_orders(csv_file):
    orders = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)
        for row in reader:
            if len(row) < 17:
                continue
            order_no = row[0].strip().replace('="','').replace('"','')
            amount = row[6].strip()
            create_time = row[14].strip()
            finish_time = row[15].strip()
            pay_status = row[16].strip()
            # 创建时间戳
            try:
                create_ts = int(datetime.datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S').timestamp())
            except Exception:
                create_ts = ''
            # 完成时间戳
            try:
                finish_ts = int(datetime.datetime.strptime(finish_time, '%Y-%m-%d %H:%M:%S').timestamp())
            except Exception:
                finish_ts = ''
            orders.append({
                'order_no': order_no,
                'amount': amount,
                'create_time': create_time,
                'create_ts': create_ts,
                'finish_time': finish_time,
                'finish_ts': finish_ts,
                'pay_status': pay_status
            })
    return orders

# 串行创建并回调
def create_and_notify(orders):
    print(f"\n开始同步创建订单并回调，共{len(orders)}条...")
    create_headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/javascript, */*; q=0.01",
        'Accept-Language': "zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7",
        'Cache-Control': "no-cache",
        'Origin': "http://**********",
        'Pragma': "no-cache",
        'Referer': "http://**********/shop.html",
        'Sec-Fetch-Dest': "empty",
        'Sec-Fetch-Mode': "cors",
        'Sec-Fetch-Site': "same-origin",
        'X-Requested-With': "XMLHttpRequest",
        'sec-ch-ua': "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"",
        'sec-ch-ua-mobile': "?0",
        'sec-ch-ua-platform': "\"Windows\""
    }
    notify_headers_base = {
        'Cookie': COOKIE,
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/javascript, */*; q=0.01",
        'Accept-Language': "zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7",
        'Cache-Control': "no-cache",
        'Origin': "http://**********",
        'Pragma': "no-cache",
        'Referer': "http://**********/shop.html",
        'Sec-Fetch-Dest': "empty",
        'Sec-Fetch-Mode': "cors",
        'Sec-Fetch-Site': "same-origin",
        'X-Requested-With': "XMLHttpRequest",
        'sec-ch-ua': "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"",
        'sec-ch-ua-mobile': "?0",
        'sec-ch-ua-platform': "\"Windows\""
    }
    notify_data = {}
    create_success, notify_success = 0, 0
    
    for idx, order in enumerate(orders, 1):
        # 1. 创建订单
        create_data = {
            'out_trade_no': order['order_no'],
            'price': order['amount'],
            'create_time': str(order['create_ts'])
        }
        try:
            resp = requests.post(CREATE_URL, headers=create_headers, data=create_data, timeout=30)

            print(f"[{idx}/{len(orders)}] 创建订单: {order['order_no']} 状态码: {resp.status_code}")
            try:
                resp_json = resp.json()
                # print(resp_json)
            except Exception:
                resp_json = {}
            if resp.status_code == 200 and resp_json.get('code') == 0:
                print(f"  ✓ 创建成功: {resp_json.get('message')}")
                create_success += 1
                # 2. 仅对已支付订单做回调
                if order['pay_status'] == '已支付':
                    notify_headers = notify_headers_base.copy()
                    notify_data['out_trade_no'] = order['order_no']
                    notify_data['pay_time'] = str(order['finish_ts'])
                    notify_data['pay_type'] = 'wechatpay'
                    try:
                        notify_resp = requests.post(NOTIFY_URL, headers=notify_headers, data=notify_data, timeout=30)

                        print(f"    → 回调订单: {order['order_no']} 状态码: {notify_resp.status_code}")
                        try:
                            notify_json = notify_resp.json()
                        except Exception:
                            notify_json = {}
                        if notify_resp.status_code == 200 and notify_json.get('code') == 0:
                            print(f"      ✓ 回调成功: {notify_json.get('message')}")
                            notify_success += 1
                        else:
                            print(f"      ✗ 回调失败:{order['order_no']}")
                    except Exception as e:
                        print(f"      ✗ 回调异常: {e}")
            else:
                print(f"  ✗ 创建失败: {order['order_no']}")
        except Exception as e:
            print(f"  ✗ 创建异常: {e}")
    print(f"\n创建成功: {create_success} 条，回调成功: {notify_success} 条")
    print("全部流程完成！")

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print('用法: python order_create_and_notify.py <csv文件>')
        sys.exit(1)
    csv_file = sys.argv[1]
    orders = extract_orders(csv_file)

    create_and_notify(orders) 