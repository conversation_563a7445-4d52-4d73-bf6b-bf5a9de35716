import pandas as pd
import re

fa_order = pd.read_csv('fa_order.csv', dtype=str)
complaints = pd.read_csv('tousu.csv', dtype=str)

order_id_col = [col for col in complaints.columns if '商家交易订单号' in col][0]
tousu_col = [col for col in complaints.columns if '投诉内容' in col][0]

def clean_oid(oid):
    oid = str(oid)
    oid = oid.replace('"', '')
    oid = re.sub(r'\s+', '', oid)
    oid = oid.replace('\u3000', '')
    oid = oid.replace('\t', '').replace('\r', '').replace('\n', '')
    oid = oid.strip()
    if 'e+' in oid or 'E+' in oid:
        try:
            oid = '{:.0f}'.format(float(oid))
        except:
            pass
    return oid

complaint_map = {}
for _, row in complaints.iterrows():
    order_ids_raw = str(row[order_id_col]).replace('"', '').replace('\t', '').replace('\r', '').replace('\n', '')
    order_ids = order_ids_raw.split(',')
    for oid in order_ids:
        oid = clean_oid(oid)
        if oid:
            complaint_map[oid] = row[tousu_col]

fa_order['dy_order_id_clean'] = fa_order['dy_order_id'].apply(clean_oid)
fa_order['tousu'] = fa_order['dy_order_id_clean'].map(complaint_map)

# 只导出有投诉内容的订单
fa_order_with_tousu = fa_order[fa_order['tousu'].notnull()]
fa_order_with_tousu.to_csv('fa_order_with_tousu_only.csv', index=False, encoding='utf-8-sig')
print('处理完成，只包含有投诉内容的订单，结果已保存为fa_order_with_tousu_only.csv')