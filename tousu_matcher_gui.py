import pandas as pd
import re
import tkinter as tk
from tkinter import filedialog, messagebox

def clean_oid(oid):
    oid = str(oid)
    oid = oid.replace('"', '')
    oid = re.sub(r'\s+', '', oid)
    oid = oid.replace('\u3000', '')
    oid = oid.replace('\t', '').replace('\r', '').replace('\n', '')
    oid = oid.strip()
    if 'e+' in oid or 'E+' in oid:
        try:
            oid = '{:.0f}'.format(float(oid))
        except:
            pass
    return oid

def read_csv_auto_encoding(path):
    try:
        return pd.read_csv(path, dtype=str, encoding='utf-8')
    except UnicodeDecodeError:
        return pd.read_csv(path, dtype=str, encoding='gbk')

def process_files(fa_order_path, tousu_path, output_path):
    fa_order = read_csv_auto_encoding(fa_order_path)
    complaints = read_csv_auto_encoding(tousu_path)

    order_id_col = [col for col in complaints.columns if '商家交易订单号' in col][0]
    tousu_col = [col for col in complaints.columns if '投诉内容' in col][0]
    time_col = [col for col in complaints.columns if '投诉时间' in col][0]

    complaint_map = {}
    for _, row in complaints.iterrows():
        order_ids_raw = str(row[order_id_col]).replace('"', '').replace('\t', '').replace('\r', '').replace('\n', '')
        order_ids = order_ids_raw.split(',')
        for oid in order_ids:
            oid = clean_oid(oid)
            if oid:
                complaint_map[oid] = {
                    "投诉内容": row[tousu_col],
                    "投诉时间": row[time_col]
                }

    # 只做临时列，不输出
    fa_order['__dy_order_id_clean'] = fa_order['dy_order_id'].apply(clean_oid)
    fa_order['tousu'] = fa_order['__dy_order_id_clean'].map(lambda x: complaint_map[x]["投诉内容"] if x in complaint_map else None)
    fa_order['tousu_time'] = fa_order['__dy_order_id_clean'].map(lambda x: complaint_map[x]["投诉时间"] if x in complaint_map else None)
    fa_order_with_tousu = fa_order[fa_order['tousu'].notnull()].copy()
    if '__dy_order_id_clean' in fa_order_with_tousu.columns:
        fa_order_with_tousu.drop(columns=['__dy_order_id_clean'], inplace=True)
    fa_order_with_tousu.to_csv(output_path, index=False, encoding='utf-8-sig')

class App:
    def __init__(self, root):
        self.root = root
        self.root.title("订单投诉匹配工具")
        self.fa_order_path = tk.StringVar()
        self.tousu_path = tk.StringVar()
        self.output_path = tk.StringVar()

        tk.Label(root, text="订单文件(fa_order.csv)：").grid(row=0, column=0, sticky='e')
        tk.Entry(root, textvariable=self.fa_order_path, width=40).grid(row=0, column=1)
        tk.Button(root, text="选择", command=self.select_fa_order).grid(row=0, column=2)

        tk.Label(root, text="投诉文件(tousu.csv)：").grid(row=1, column=0, sticky='e')
        tk.Entry(root, textvariable=self.tousu_path, width=40).grid(row=1, column=1)
        tk.Button(root, text="选择", command=self.select_tousu).grid(row=1, column=2)

        tk.Label(root, text="输出文件：").grid(row=2, column=0, sticky='e')
        tk.Entry(root, textvariable=self.output_path, width=40).grid(row=2, column=1)
        tk.Button(root, text="保存为", command=self.select_output).grid(row=2, column=2)

        tk.Button(root, text="开始处理", command=self.run).grid(row=3, column=1, pady=10)

    def select_fa_order(self):
        path = filedialog.askopenfilename(title="选择订单文件", filetypes=[("CSV文件", "*.csv")])
        if path:
            self.fa_order_path.set(path)

    def select_tousu(self):
        path = filedialog.askopenfilename(title="选择投诉文件", filetypes=[("CSV文件", "*.csv")])
        if path:
            self.tousu_path.set(path)

    def select_output(self):
        path = filedialog.asksaveasfilename(title="保存输出文件", defaultextension=".csv", filetypes=[("CSV文件", "*.csv")])
        if path:
            self.output_path.set(path)

    def run(self):
        fa_order = self.fa_order_path.get()
        tousu = self.tousu_path.get()
        output = self.output_path.get()
        if not fa_order or not tousu or not output:
            messagebox.showerror("错误", "请先选择所有文件！")
            return
        try:
            process_files(fa_order, tousu, output)
            messagebox.showinfo("完成", f"处理完成，结果已保存为：\n{output}")
        except Exception as e:
            messagebox.showerror("出错", f"处理失败：{e}")

if __name__ == '__main__':
    root = tk.Tk()
    app = App(root)
    root.mainloop() 