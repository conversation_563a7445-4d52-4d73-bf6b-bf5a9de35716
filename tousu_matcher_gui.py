import pandas as pd
import re
import tkinter as tk
from tkinter import filedialog, messagebox

def clean_oid(oid):
    if pd.isna(oid) or oid is None:
        return ""
    oid = str(oid)
    oid = oid.replace('"', '')
    oid = re.sub(r'\s+', '', oid)
    oid = oid.replace('\u3000', '')
    oid = oid.replace('\t', '').replace('\r', '').replace('\n', '')
    oid = oid.strip()
    if 'e+' in oid or 'E+' in oid:
        try:
            oid = '{:.0f}'.format(float(oid))
        except:
            pass
    # 确保返回的是有效的字符串，如果是空的或者无效的，返回空字符串
    if not oid or oid.lower() in ['nan', 'none', 'null']:
        return ""
    return oid

def read_csv_auto_encoding(path):
    try:
        return pd.read_csv(path, dtype=str, encoding='utf-8')
    except UnicodeDecodeError:
        return pd.read_csv(path, dtype=str, encoding='gbk')

def process_files(fa_order_path, tousu_path, output_path):
    fa_order = read_csv_auto_encoding(fa_order_path)
    complaints = read_csv_auto_encoding(tousu_path)

    order_id_col = [col for col in complaints.columns if '商家交易订单号' in col][0]
    tousu_col = [col for col in complaints.columns if '投诉内容' in col][0]
    time_col = [col for col in complaints.columns if '投诉时间' in col][0]

    complaint_map = {}
    used_complaint_indices = set()  # 记录被匹配的投诉记录索引

    for idx, row in complaints.iterrows():
        order_ids_raw = str(row[order_id_col]).replace('"', '').replace('\t', '').replace('\r', '').replace('\n', '')
        order_ids = order_ids_raw.split(',')
        for oid in order_ids:
            oid = clean_oid(oid)
            if oid and oid.strip():  # 确保不是空字符串
                complaint_map[oid] = {
                    "投诉内容": row[tousu_col],
                    "投诉时间": row[time_col],
                    "complaint_idx": idx  # 记录投诉记录的索引
                }

    # 只做临时列，不输出
    fa_order['__dy_order_id_clean'] = fa_order['dy_order_id'].apply(clean_oid)
    def get_complaint_content(oid):
        if oid and oid.strip() and oid in complaint_map:
            return complaint_map[oid]["投诉内容"]
        return None

    def get_complaint_time(oid):
        if oid and oid.strip() and oid in complaint_map:
            return complaint_map[oid]["投诉时间"]
        return None

    fa_order['tousu'] = fa_order['__dy_order_id_clean'].map(get_complaint_content)
    fa_order['tousu_time'] = fa_order['__dy_order_id_clean'].map(get_complaint_time)

    # 记录哪些投诉被匹配了
    for _, row in fa_order.iterrows():
        order_id_clean = row['__dy_order_id_clean']
        if order_id_clean and order_id_clean.strip() and order_id_clean in complaint_map:
            used_complaint_indices.add(complaint_map[order_id_clean]["complaint_idx"])

    # 获取匹配成功的订单
    fa_order_matched = fa_order[fa_order['tousu'].notnull()].copy()
    if '__dy_order_id_clean' in fa_order_matched.columns:
        fa_order_matched.drop(columns=['__dy_order_id_clean'], inplace=True)

    # 处理未匹配的投诉
    unmatched_complaints = complaints[~complaints.index.isin(used_complaint_indices)]

    # 为未匹配的投诉创建数据行
    unmatched_rows = []
    for _, complaint_row in unmatched_complaints.iterrows():
        # 创建一个新行，订单字段用"???"填充
        new_row = {}
        for col in fa_order.columns:
            if col == '__dy_order_id_clean':
                continue  # 跳过临时列
            elif col == 'tousu':
                new_row[col] = complaint_row[tousu_col]
            elif col == 'tousu_time':
                new_row[col] = complaint_row[time_col]
            else:
                new_row[col] = "???"
        unmatched_rows.append(new_row)

    # 合并数据
    if unmatched_rows:
        unmatched_df = pd.DataFrame(unmatched_rows)
        final_result = pd.concat([fa_order_matched, unmatched_df], ignore_index=True)
    else:
        final_result = fa_order_matched

    # 保存到文件
    final_result.to_csv(output_path, index=False, encoding='utf-8-sig')

class App:
    def __init__(self, root):
        self.root = root
        self.root.title("订单投诉匹配工具")
        self.fa_order_path = tk.StringVar()
        self.tousu_path = tk.StringVar()
        self.output_path = tk.StringVar()

        tk.Label(root, text="订单文件(fa_order.csv)：").grid(row=0, column=0, sticky='e')
        tk.Entry(root, textvariable=self.fa_order_path, width=40).grid(row=0, column=1)
        tk.Button(root, text="选择", command=self.select_fa_order).grid(row=0, column=2)

        tk.Label(root, text="投诉文件(tousu.csv)：").grid(row=1, column=0, sticky='e')
        tk.Entry(root, textvariable=self.tousu_path, width=40).grid(row=1, column=1)
        tk.Button(root, text="选择", command=self.select_tousu).grid(row=1, column=2)

        tk.Label(root, text="输出文件：").grid(row=2, column=0, sticky='e')
        tk.Entry(root, textvariable=self.output_path, width=40).grid(row=2, column=1)
        tk.Button(root, text="保存为", command=self.select_output).grid(row=2, column=2)

        tk.Button(root, text="开始处理", command=self.run).grid(row=3, column=1, pady=10)

    def select_fa_order(self):
        path = filedialog.askopenfilename(title="选择订单文件", filetypes=[("CSV文件", "*.csv")])
        if path:
            self.fa_order_path.set(path)

    def select_tousu(self):
        path = filedialog.askopenfilename(title="选择投诉文件", filetypes=[("CSV文件", "*.csv")])
        if path:
            self.tousu_path.set(path)

    def select_output(self):
        path = filedialog.asksaveasfilename(title="保存输出文件", defaultextension=".csv", filetypes=[("CSV文件", "*.csv")])
        if path:
            self.output_path.set(path)

    def run(self):
        fa_order = self.fa_order_path.get()
        tousu = self.tousu_path.get()
        output = self.output_path.get()
        if not fa_order or not tousu or not output:
            messagebox.showerror("错误", "请先选择所有文件！")
            return
        try:
            process_files(fa_order, tousu, output)
            messagebox.showinfo("完成", f"处理完成，结果已保存为：\n{output}")
        except Exception as e:
            messagebox.showerror("出错", f"处理失败：{e}")

if __name__ == '__main__':
    root = tk.Tk()
    app = App(root)
    root.mainloop() 