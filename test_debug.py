import pandas as pd
import re

def clean_oid(oid):
    if pd.isna(oid) or oid is None:
        return ""
    oid = str(oid)
    oid = oid.replace('"', '')
    oid = re.sub(r'\s+', '', oid)
    oid = oid.replace('\u3000', '')
    oid = oid.replace('\t', '').replace('\r', '').replace('\n', '')
    oid = oid.strip()
    if 'e+' in oid or 'E+' in oid:
        try:
            oid = '{:.0f}'.format(float(oid))
        except:
            pass
    # 确保返回的是有效的字符串，如果是空的或者无效的，返回空字符串
    if not oid or oid.lower() in ['nan', 'none', 'null']:
        return ""
    return oid

# 测试数据
test_data = pd.DataFrame({
    'dy_order_id': ['123', '456', None, '', 'nan']
})

print("测试clean_oid函数:")
test_data['clean'] = test_data['dy_order_id'].apply(clean_oid)
print(test_data)

# 测试map函数
complaint_map = {'123': {'投诉内容': '测试投诉', '投诉时间': '2023-01-01'}}

def get_complaint_content(oid):
    if oid and oid.strip() and oid in complaint_map:
        return complaint_map[oid]["投诉内容"]
    return None

print("\n测试map函数:")
test_data['tousu'] = test_data['clean'].map(get_complaint_content)
print(test_data)
